# INA226 Power Monitor Module

## 概述

INA226是一款高精度的双向电流/功率监控器，支持I2C接口。本模块实现了INA226的完整驱动程序，支持电压、电流、功率的差分测量。

## 硬件连接

### I2C连接
- **SCL**: PB6 (I2C1_SCL)
- **SDA**: PB7 (I2C1_SDA)
- **VCC**: 3.3V
- **GND**: GND

### INA226引脚配置
- **A1, A0**: 接地 (I2C地址 = 0x40)
- **VS+**: 被测电源正极
- **VS-**: 被测电源负极
- **IN+**: 分流电阻正极
- **IN-**: 分流电阻负极

### 分流电阻
- 默认配置：750mΩ (0.75Ω) - R750
- 可根据需要修改 `INA226_SHUNT_RESISTANCE_OHMS` 宏定义

## 文件结构

```
User/
├── mod_ina226.h          # INA226模块头文件
├── mod_ina226.c          # INA226模块实现
├── ina226_example.h      # 使用示例头文件
├── ina226_example.c      # 使用示例实现
└── INA226_README.md      # 本说明文件
```

## 主要功能

### 1. 初始化
```c
bool INA226_Init(void);
```
- 初始化I2C接口
- 检测INA226设备
- 配置默认参数
- 设置校准值

### 2. 数据读取
```c
// 读取所有测量数据
bool INA226_ReadData(INA226_Data_t *data);

// 单独读取各参数
float INA226_ReadBusVoltage(void);      // 总线电压 (V)
float INA226_ReadShuntVoltage(void);    // 分流电压 (mV)
float INA226_ReadCurrent(void);         // 电流 (A)
float INA226_ReadPower(void);           // 功率 (W)
```

### 3. 配置管理
```c
// 自定义配置
bool INA226_Configure(const INA226_Config_t *config);

// 复位设备
bool INA226_Reset(void);

// 检测设备存在
bool INA226_IsPresent(void);
```

## 配置选项

### 平均模式
- `INA226_CONFIG_AVG_1`: 1次采样
- `INA226_CONFIG_AVG_4`: 4次采样平均
- `INA226_CONFIG_AVG_16`: 16次采样平均
- `INA226_CONFIG_AVG_64`: 64次采样平均
- `INA226_CONFIG_AVG_128`: 128次采样平均
- `INA226_CONFIG_AVG_256`: 256次采样平均
- `INA226_CONFIG_AVG_512`: 512次采样平均
- `INA226_CONFIG_AVG_1024`: 1024次采样平均

### 转换时间
- `INA226_CONFIG_VBUSCT_140US`: 140µs
- `INA226_CONFIG_VBUSCT_204US`: 204µs
- `INA226_CONFIG_VBUSCT_332US`: 332µs
- `INA226_CONFIG_VBUSCT_588US`: 588µs
- `INA226_CONFIG_VBUSCT_1100US`: 1.1ms (默认)
- `INA226_CONFIG_VBUSCT_2116US`: 2.116ms
- `INA226_CONFIG_VBUSCT_4156US`: 4.156ms
- `INA226_CONFIG_VBUSCT_8244US`: 8.244ms

### 工作模式
- `INA226_CONFIG_MODE_POWER_DOWN`: 掉电模式
- `INA226_CONFIG_MODE_SHUNT_TRIG`: 分流电压触发测量
- `INA226_CONFIG_MODE_BUS_TRIG`: 总线电压触发测量
- `INA226_CONFIG_MODE_SHUNT_BUS_TRIG`: 分流和总线电压触发测量
- `INA226_CONFIG_MODE_SHUNT_CONT`: 分流电压连续测量
- `INA226_CONFIG_MODE_BUS_CONT`: 总线电压连续测量
- `INA226_CONFIG_MODE_SHUNT_BUS_CONT`: 分流和总线电压连续测量 (默认)

## 使用示例

### 基本使用
```c
#include "mod_ina226.h"

int main(void)
{
    INA226_Data_t data;
    
    // 初始化
    if (INA226_Init()) {
        printf("INA226 初始化成功\n");
        
        // 读取数据
        if (INA226_ReadData(&data)) {
            printf("电压: %.3fV\n", data.voltage_V);
            printf("电流: %.3fA\n", data.current_A);
            printf("功率: %.3fW\n", data.power_W);
            printf("分流电压: %.3fmV\n", data.shunt_voltage_mV);
        }
    }
    
    return 0;
}
```

### 高精度配置
```c
INA226_Config_t config;
config.avg_mode = INA226_CONFIG_AVG_128;           // 128次平均
config.bus_conv_time = INA226_CONFIG_VBUSCT_8244US; // 8.244ms转换时间
config.shunt_conv_time = INA226_CONFIG_VSHCT_8244US; // 8.244ms转换时间
config.mode = INA226_CONFIG_MODE_SHUNT_BUS_CONT;    // 连续测量

INA226_Configure(&config);
```

## 技术规格

### 测量范围
- **总线电压**: 0 ~ 36V
- **分流电压**: ±81.92mV
- **电流**: 取决于分流电阻值
- **功率**: 电压 × 电流

### 精度
- **总线电压**: ±0.1% (典型值)
- **分流电压**: ±0.1% (典型值)
- **电流**: 取决于分流电阻精度
- **功率**: 取决于电压和电流精度

### 分辨率
- **总线电压**: 1.25mV/bit
- **分流电压**: 2.5µV/bit
- **电流**: 100µA/bit (750mΩ分流电阻)
- **功率**: 2.5mW/bit (750mΩ分流电阻)

## 注意事项

1. **I2C地址**: 默认为0x40，可通过A1、A0引脚修改
2. **分流电阻**: 需要根据实际电流范围选择合适的分流电阻值
3. **校准值**: 更换分流电阻后需要重新计算校准值
4. **电源电压**: INA226工作电压为2.7V~5.5V
5. **测量电压**: 总线电压不能超过36V
6. **差分测量**: 支持双向电流测量，正负电流都能准确测量

## 故障排除

### 初始化失败
1. 检查I2C连接是否正确
2. 检查设备地址是否匹配
3. 检查电源供电是否正常

### 读取数据异常
1. 检查分流电阻连接
2. 检查校准值设置
3. 检查测量范围是否超限

### 精度不够
1. 增加平均次数
2. 延长转换时间
3. 检查分流电阻精度
4. 减少环境干扰

## 更新日志

- **v1.0.0** (2025-07-02): 初始版本
  - 实现基本的电压、电流、功率测量功能
  - 支持I2C通信
  - 提供配置管理功能
  - 包含使用示例和测试程序
