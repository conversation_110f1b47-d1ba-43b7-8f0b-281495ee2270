# INA226 UI Integration

## 概述

本文档说明如何将INA226电源监控模块与LCD显示界面集成，实现实时数据显示功能。

## 功能特性

✅ **实时数据显示**: INA226测量数据实时更新到LCD屏幕  
✅ **自动更新**: 每秒自动读取并更新显示数据  
✅ **错误处理**: 读取失败时的错误处理机制  
✅ **模块化设计**: UI和INA226模块独立，便于维护  
✅ **测试模式**: 支持虚拟数据测试UI功能  

## 文件结构

```
User/
├── mod_ina226.c/.h                    # INA226驱动模块
├── ui_demo.c/.h                       # UI演示模块
├── ina226_ui_integration_example.c/.h # 集成示例
└── INA226_UI_INTEGRATION_README.md    # 本说明文件
```

## 核心功能

### 1. UI数据更新函数

```c
void ui_update_ina226_data(float voltage, float current, float power, float shunt_voltage);
```

**功能**: 更新LCD上的INA226数据显示  
**参数**:
- `voltage`: 总线电压 (V)
- `current`: 电流 (A)  
- `power`: 功率 (W)
- `shunt_voltage`: 分流电压 (mV)

### 2. UI界面创建函数

```c
void ui_create_ina226_display(void);
```

**功能**: 创建INA226数据显示界面  
**显示内容**:
- 标题: "INA226 Power Monitor"
- 电压显示: "Voltage: X.XXX V"
- 电流显示: "Current: X.XXX A"  
- 功率显示: "Power: X.XXX W"
- 分流电压显示: "Shunt: X.XXX mV"

## 使用方法

### 基本集成步骤

1. **初始化模块**
```c
// 初始化INA226
if (!INA226_Init()) {
    logi("INA226 initialization failed\n");
    return;
}

// 初始化UI模块
ui_demo_init();
```

2. **创建显示界面**
```c
ui_create_ina226_display();
```

3. **读取数据并更新显示**
```c
INA226_Data_t data;

if (INA226_ReadData(&data)) {
    // 更新LCD显示
    ui_update_ina226_data(data.voltage_V, 
                          data.current_A, 
                          data.power_W, 
                          data.shunt_voltage_mV);
}
```

### 在main.c中的集成

当前main.c已经集成了以下功能：

```c
// 初始化部分
ui_demo_init();
ui_create_ina226_display();

// 主循环中的数据更新
if (INA226_ReadData(&ina226_data)) {
    // 串口输出
    logi("INA226 - Voltage: %.3fV, Current: %.3fA, Power: %.3fW, Shunt: %.3fmV\r\n",
         ina226_data.voltage_V, ina226_data.current_A, 
         ina226_data.power_W, ina226_data.shunt_voltage_mV);
    
    // LCD显示更新
    ui_update_ina226_data(ina226_data.voltage_V, 
                          ina226_data.current_A, 
                          ina226_data.power_W, 
                          ina226_data.shunt_voltage_mV);
}
```

## 显示效果

LCD屏幕将显示如下内容：

```
    INA226 Power Monitor

    Voltage: 3.300 V

    Current: 0.150 A

    Power: 0.495 W

    Shunt: 15.000 mV
```

## 测试功能

### 1. 虚拟数据测试

```c
// 使用虚拟数据测试UI更新
ui_test_ina226_update();
```

### 2. 完整集成测试

```c
// 运行所有集成示例
INA226_UI_RunAllExamples();
```

## FreeRTOS任务集成

可以创建专门的任务来处理INA226数据读取和UI更新：

```c
void INA226_UI_IntegrationTask(void *pvParameters)
{
    INA226_Data_t data;
    
    // 初始化
    INA226_Init();
    ui_demo_init();
    ui_create_ina226_display();
    
    // 主循环
    while (1) {
        if (INA226_ReadData(&data)) {
            ui_update_ina226_data(data.voltage_V, data.current_A, 
                                  data.power_W, data.shunt_voltage_mV);
        }
        vTaskDelay(pdMS_TO_TICKS(1000)); // 1秒更新一次
    }
}

// 创建任务
xTaskCreate(INA226_UI_IntegrationTask, "INA226_UI", 256*4, NULL, 3, NULL);
```

## 错误处理

### 1. INA226读取失败

```c
if (INA226_ReadData(&data)) {
    // 正常更新
    ui_update_ina226_data(data.voltage_V, data.current_A, data.power_W, data.shunt_voltage_mV);
} else {
    // 错误处理 - 可以显示错误信息或使用上次的数据
    logi("INA226 read error\n");
}
```

### 2. UI对象无效

UI更新函数内部已包含空指针检查：

```c
if (voltage_label != NULL) {
    sprintf(buf, "Voltage: %.3f V", voltage);
    lv_label_set_text(voltage_label, buf);
}
```

## 性能优化

### 1. 更新频率控制

```c
static uint32_t last_update_time = 0;
uint32_t current_time = get_system_time();

// 限制更新频率为1Hz
if (current_time - last_update_time >= 1000) {
    ui_update_ina226_data(...);
    last_update_time = current_time;
}
```

### 2. 数据变化检测

```c
static INA226_Data_t last_data = {0};

if (memcmp(&data, &last_data, sizeof(INA226_Data_t)) != 0) {
    // 数据有变化才更新显示
    ui_update_ina226_data(...);
    last_data = data;
}
```

## 自定义扩展

### 1. 添加更多显示项

```c
// 在ui_demo.c中添加新的标签
static lv_obj_t *efficiency_label = NULL;

// 在ui_create_ina226_display中创建
efficiency_label = lv_label_create(scr);
lv_label_set_text(efficiency_label, "Efficiency: -- %");

// 在ui_update_ina226_data中更新
float efficiency = (power / (voltage * current)) * 100.0f;
sprintf(buf, "Efficiency: %.1f %%", efficiency);
lv_label_set_text(efficiency_label, buf);
```

### 2. 添加图表显示

```c
// 使用LVGL图表组件显示历史数据
lv_obj_t *chart = lv_chart_create(scr);
lv_chart_series_t *voltage_series = lv_chart_add_series(chart, lv_palette_main(LV_PALETTE_RED), LV_CHART_AXIS_PRIMARY_Y);

// 在数据更新时添加到图表
lv_chart_set_next_value(chart, voltage_series, (int32_t)(voltage * 1000));
```

## 注意事项

1. **LVGL线程安全**: 确保UI更新在LVGL任务中执行
2. **内存管理**: 注意标签对象的生命周期管理
3. **更新频率**: 避免过于频繁的UI更新影响性能
4. **数据格式**: 注意浮点数显示精度和格式
5. **错误恢复**: 实现适当的错误恢复机制

## 故障排除

### 显示不更新
1. 检查UI模块是否正确初始化
2. 确认标签对象指针有效
3. 验证LVGL定时器是否正常运行

### 数据显示异常
1. 检查INA226数据读取是否正常
2. 确认数据格式和精度设置
3. 验证sprintf格式字符串

### 性能问题
1. 检查更新频率是否过高
2. 优化字符串格式化操作
3. 考虑使用数据变化检测

## 更新日志

- **v1.0.0** (2025-07-02): 初始版本
  - 实现INA226数据到LCD的实时显示
  - 集成到main.c主循环
  - 提供测试和示例功能
  - 支持FreeRTOS任务集成
