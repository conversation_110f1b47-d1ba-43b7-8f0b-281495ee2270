/**
 ******************************************************************************
 * @file    ina226_example.h
 * <AUTHOR>
 * @version V1.0.0
 * @date    2025-07-02
 * @brief   INA226 usage examples header file
 ******************************************************************************
 */

#ifndef __INA226_EXAMPLE_H
#define __INA226_EXAMPLE_H

#ifdef __cplusplus
extern "C" {
#endif

/* Includes ------------------------------------------------------------------*/
#include "mod_ina226.h"

/* Exported types ------------------------------------------------------------*/
/* Exported constants --------------------------------------------------------*/
/* Exported macro ------------------------------------------------------------*/
/* Exported functions --------------------------------------------------------*/

/**
 * @brief Example 1: Basic voltage and current measurement
 */
void INA226_Example_BasicMeasurement(void);

/**
 * @brief Example 2: Individual parameter reading
 */
void INA226_Example_IndividualReading(void);

/**
 * @brief Example 3: Custom configuration
 */
void INA226_Example_CustomConfig(void);

/**
 * @brief Example 4: Power monitoring with thresholds
 */
void INA226_Example_PowerMonitoring(void);

/**
 * @brief Run all INA226 examples
 */
void INA226_RunAllExamples(void);

#ifdef __cplusplus
}
#endif

#endif /* __INA226_EXAMPLE_H */
