/**
 ******************************************************************************
 * @file    ui_demo_example.c
 * <AUTHOR>
 * @version V1.0.0
 * @date    2025-07-02
 * @brief   UI demo usage examples
 ******************************************************************************
 */

/* Includes ------------------------------------------------------------------*/
#include "ui_demo.h"
#include "mod_ina226.h"
#include "app_util.h"

/* Private typedef -----------------------------------------------------------*/
/* Private define ------------------------------------------------------------*/
/* Private macro -------------------------------------------------------------*/
/* Private variables ---------------------------------------------------------*/
/* Private function prototypes -----------------------------------------------*/
/* Private functions ---------------------------------------------------------*/

/**
 * @brief Example 1: Basic UI test
 */
void UI_Example_BasicTest(void)
{
    logi("=== UI Basic Test Example ===\r\n");
    
    // Initialize UI demo module
    ui_demo_init();
    
    // Create basic test UI
    ui_test();
    
    logi("Basic UI test created\r\n");
}

/**
 * @brief Example 2: INA226 data display UI
 */
void UI_Example_INA226Display(void)
{
    logi("=== UI INA226 Display Example ===\r\n");
    
    // Create INA226 display UI
    ui_create_ina226_display();
    
    logi("INA226 display UI created\r\n");
}

/**
 * @brief Example 3: Combined UI with data updates
 */
void UI_Example_CombinedWithData(void)
{
    INA226_Data_t ina226_data;
    
    logi("=== UI Combined with Data Example ===\r\n");
    
    // Initialize modules
    ui_demo_init();
    
    if (!INA226_Init()) {
        logi("ERROR: INA226 initialization failed!\r\n");
        return;
    }
    
    // Create INA226 display UI
    ui_create_ina226_display();
    
    // Simulate data updates for 10 cycles
    for (int i = 0; i < 10; i++) {
        if (INA226_ReadData(&ina226_data)) {
            // Update UI with real data
            ui_update_ina226_data(ina226_data.voltage_V, 
                                  ina226_data.current_A, 
                                  ina226_data.power_W, 
                                  ina226_data.shunt_voltage_mV);
            
            logi("Cycle %d: UI updated with real INA226 data\r\n", i + 1);
        } else {
            // Update UI with dummy data
            ui_update_ina226_data(3.3f + (i * 0.1f), 
                                  0.1f + (i * 0.01f), 
                                  0.33f + (i * 0.01f), 
                                  10.0f + (i * 1.0f));
            
            logi("Cycle %d: UI updated with dummy data\r\n", i + 1);
        }
        
        s_delay_ms(1000); // Wait 1 second
    }
    
    logi("Combined UI example completed\r\n");
}

/**
 * @brief Example 4: UI module lifecycle management
 */
void UI_Example_LifecycleManagement(void)
{
    logi("=== UI Lifecycle Management Example ===\r\n");
    
    // Initialize UI demo module
    logi("Initializing UI demo module...\r\n");
    ui_demo_init();
    
    // Create UI
    logi("Creating UI...\r\n");
    ui_test();
    
    // Simulate some work
    s_delay_ms(2000);
    
    // Cleanup resources
    logi("Cleaning up UI demo module...\r\n");
    ui_demo_cleanup();
    
    logi("UI lifecycle management example completed\r\n");
}

/**
 * @brief Run all UI demo examples
 */
void UI_RunAllExamples(void)
{
    logi("\r\n");
    logi("************************************************\r\n");
    logi("*           UI Demo Examples Starting          *\r\n");
    logi("************************************************\r\n");
    
    // Example 1: Basic UI test
    UI_Example_BasicTest();
    s_delay_ms(2000);
    
    // Example 2: INA226 display
    UI_Example_INA226Display();
    s_delay_ms(2000);
    
    // Example 3: Combined with data
    UI_Example_CombinedWithData();
    s_delay_ms(2000);
    
    // Example 4: Lifecycle management
    UI_Example_LifecycleManagement();
    
    logi("\r\n");
    logi("************************************************\r\n");
    logi("*           UI Demo Examples Completed         *\r\n");
    logi("************************************************\r\n");
}

/**
 * @brief Simple UI task for FreeRTOS
 * @param pvParameters: Task parameters (unused)
 */
void UI_Task(void *pvParameters)
{
    logi("UI Task started\r\n");
    
    // Initialize UI
    ui_demo_init();
    ui_test();
    
    // Main UI task loop
    while (1) {
        // Handle UI updates here if needed
        // For now, just a simple delay
        vTaskDelay(pdMS_TO_TICKS(100));
    }
}

/**
 * @brief INA226 data display task for FreeRTOS
 * @param pvParameters: Task parameters (unused)
 */
void INA226_UI_Task(void *pvParameters)
{
    INA226_Data_t ina226_data;
    
    logi("INA226 UI Task started\r\n");
    
    // Initialize modules
    if (!INA226_Init()) {
        logi("ERROR: INA226 initialization failed in UI task!\r\n");
        vTaskDelete(NULL);
        return;
    }
    
    // Create INA226 display UI
    ui_create_ina226_display();
    
    // Main data update loop
    while (1) {
        if (INA226_ReadData(&ina226_data)) {
            ui_update_ina226_data(ina226_data.voltage_V, 
                                  ina226_data.current_A, 
                                  ina226_data.power_W, 
                                  ina226_data.shunt_voltage_mV);
        }
        
        vTaskDelay(pdMS_TO_TICKS(1000)); // Update every 1 second
    }
}
