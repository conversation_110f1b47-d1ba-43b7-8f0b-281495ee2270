/**
 ******************************************************************************
 * @file    ina226_ui_integration_example.c
 * <AUTHOR>
 * @version V1.0.0
 * @date    2025-07-02
 * @brief   INA226 and UI integration examples
 ******************************************************************************
 */

/* Includes ------------------------------------------------------------------*/
#include "mod_ina226.h"
#include "ui_demo.h"
#include "app_util.h"
#include "app_uart.h"

/* Private typedef -----------------------------------------------------------*/
/* Private define ------------------------------------------------------------*/
/* Private macro -------------------------------------------------------------*/
/* Private variables ---------------------------------------------------------*/
/* Private function prototypes -----------------------------------------------*/
/* Private functions ---------------------------------------------------------*/

/**
 * @brief Example 1: Basic INA226 to LCD display integration
 */
void INA226_UI_Example_BasicIntegration(void)
{
    INA226_Data_t data;
    
    logi("=== INA226 UI Basic Integration Example ===\r\n");
    
    // Initialize modules
    if (!INA226_Init()) {
        logi("ERROR: INA226 initialization failed!\r\n");
        return;
    }
    
    ui_demo_init();
    ui_create_ina226_display();
    
    // Read and display data 10 times
    for (int i = 0; i < 10; i++) {
        if (INA226_ReadData(&data)) {
            // Update LCD display
            ui_update_ina226_data(data.voltage_V, 
                                  data.current_A, 
                                  data.power_W, 
                                  data.shunt_voltage_mV);
            
            logi("Cycle %d: Data displayed on LCD\r\n", i + 1);
        } else {
            logi("Cycle %d: Failed to read INA226 data\r\n", i + 1);
        }
        
        s_delay_ms(1000); // Wait 1 second
    }
    
    logi("Basic integration example completed\r\n");
}

/**
 * @brief Example 2: Continuous monitoring with LCD updates
 */
void INA226_UI_Example_ContinuousMonitoring(void)
{
    INA226_Data_t data;
    uint32_t cycle_count = 0;
    
    logi("=== INA226 UI Continuous Monitoring Example ===\r\n");
    
    // Initialize modules
    if (!INA226_Init()) {
        logi("ERROR: INA226 initialization failed!\r\n");
        return;
    }
    
    ui_demo_init();
    ui_create_ina226_display();
    
    logi("Starting continuous monitoring (60 seconds)...\r\n");
    
    // Monitor for 60 seconds
    while (cycle_count < 60) {
        if (INA226_ReadData(&data)) {
            // Update LCD display
            ui_update_ina226_data(data.voltage_V, 
                                  data.current_A, 
                                  data.power_W, 
                                  data.shunt_voltage_mV);
            
            // Log every 10 seconds
            if (cycle_count % 10 == 0) {
                logi("Time %ds: V=%.3fV, I=%.3fA, P=%.3fW\r\n", 
                     cycle_count, data.voltage_V, data.current_A, data.power_W);
            }
        } else {
            logi("Time %ds: Read error\r\n", cycle_count);
        }
        
        cycle_count++;
        s_delay_ms(1000); // Wait 1 second
    }
    
    logi("Continuous monitoring example completed\r\n");
}

/**
 * @brief Example 3: Test mode with dummy data
 */
void INA226_UI_Example_TestMode(void)
{
    logi("=== INA226 UI Test Mode Example ===\r\n");
    
    // Initialize UI only (no INA226 needed for test)
    ui_demo_init();
    ui_create_ina226_display();
    
    logi("Running test mode with dummy data...\r\n");
    
    // Run test updates 20 times
    for (int i = 0; i < 20; i++) {
        ui_test_ina226_update();
        s_delay_ms(500); // Wait 0.5 second
    }
    
    logi("Test mode example completed\r\n");
}

/**
 * @brief Example 4: Error handling and recovery
 */
void INA226_UI_Example_ErrorHandling(void)
{
    INA226_Data_t data;
    int error_count = 0;
    int success_count = 0;
    
    logi("=== INA226 UI Error Handling Example ===\r\n");
    
    // Initialize modules
    if (!INA226_Init()) {
        logi("ERROR: INA226 initialization failed!\r\n");
        // Continue with test mode
        ui_demo_init();
        ui_create_ina226_display();
        
        logi("Switching to test mode due to INA226 error...\r\n");
        for (int i = 0; i < 10; i++) {
            ui_test_ina226_update();
            s_delay_ms(1000);
        }
        return;
    }
    
    ui_demo_init();
    ui_create_ina226_display();
    
    // Try to read data 20 times with error handling
    for (int i = 0; i < 20; i++) {
        if (INA226_ReadData(&data)) {
            // Update LCD display
            ui_update_ina226_data(data.voltage_V, 
                                  data.current_A, 
                                  data.power_W, 
                                  data.shunt_voltage_mV);
            success_count++;
        } else {
            // Use dummy data on error
            ui_update_ina226_data(3.3f, 0.1f, 0.33f, 10.0f);
            error_count++;
            logi("Read error %d, using dummy data\r\n", error_count);
        }
        
        s_delay_ms(500);
    }
    
    logi("Error handling example completed\r\n");
    logi("Success: %d, Errors: %d\r\n", success_count, error_count);
}

/**
 * @brief Run all INA226 UI integration examples
 */
void INA226_UI_RunAllExamples(void)
{
    logi("\r\n");
    logi("************************************************\r\n");
    logi("*      INA226 UI Integration Examples          *\r\n");
    logi("************************************************\r\n");
    
    // Example 1: Basic integration
    INA226_UI_Example_BasicIntegration();
    s_delay_ms(2000);
    
    // Example 2: Continuous monitoring
    INA226_UI_Example_ContinuousMonitoring();
    s_delay_ms(2000);
    
    // Example 3: Test mode
    INA226_UI_Example_TestMode();
    s_delay_ms(2000);
    
    // Example 4: Error handling
    INA226_UI_Example_ErrorHandling();
    
    logi("\r\n");
    logi("************************************************\r\n");
    logi("*      INA226 UI Integration Completed         *\r\n");
    logi("************************************************\r\n");
}

/**
 * @brief FreeRTOS task for INA226 UI integration
 * @param pvParameters: Task parameters (unused)
 */
void INA226_UI_IntegrationTask(void *pvParameters)
{
    INA226_Data_t data;
    
    logi("INA226 UI Integration Task started\r\n");
    
    // Initialize modules
    if (!INA226_Init()) {
        logi("ERROR: INA226 initialization failed in task!\r\n");
        vTaskDelete(NULL);
        return;
    }
    
    ui_demo_init();
    ui_create_ina226_display();
    
    // Main integration loop
    while (1) {
        if (INA226_ReadData(&data)) {
            // Update LCD display
            ui_update_ina226_data(data.voltage_V, 
                                  data.current_A, 
                                  data.power_W, 
                                  data.shunt_voltage_mV);
        } else {
            logi("INA226 read error in task\r\n");
        }
        
        vTaskDelay(pdMS_TO_TICKS(1000)); // Update every 1 second
    }
}
