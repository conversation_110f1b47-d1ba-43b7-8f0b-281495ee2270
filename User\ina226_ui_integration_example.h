/**
 ******************************************************************************
 * @file    ina226_ui_integration_example.h
 * <AUTHOR>
 * @version V1.0.0
 * @date    2025-07-02
 * @brief   INA226 and UI integration examples header file
 ******************************************************************************
 */

#ifndef __INA226_UI_INTEGRATION_EXAMPLE_H
#define __INA226_UI_INTEGRATION_EXAMPLE_H

#ifdef __cplusplus
extern "C" {
#endif

/* Includes ------------------------------------------------------------------*/
#include "mod_ina226.h"
#include "ui_demo.h"
#include "FreeRTOS.h"
#include "task.h"

/* Exported types ------------------------------------------------------------*/
/* Exported constants --------------------------------------------------------*/
/* Exported macro ------------------------------------------------------------*/
/* Exported functions --------------------------------------------------------*/

/**
 * @brief Example 1: Basic INA226 to LCD display integration
 */
void INA226_UI_Example_BasicIntegration(void);

/**
 * @brief Example 2: Continuous monitoring with LCD updates
 */
void INA226_UI_Example_ContinuousMonitoring(void);

/**
 * @brief Example 3: Test mode with dummy data
 */
void INA226_UI_Example_TestMode(void);

/**
 * @brief Example 4: Error handling and recovery
 */
void INA226_UI_Example_ErrorHandling(void);

/**
 * @brief Run all INA226 UI integration examples
 */
void INA226_UI_RunAllExamples(void);

/**
 * @brief FreeRTOS task for INA226 UI integration
 * @param pvParameters: Task parameters (unused)
 */
void INA226_UI_IntegrationTask(void *pvParameters);

#ifdef __cplusplus
}
#endif

#endif /* __INA226_UI_INTEGRATION_EXAMPLE_H */
