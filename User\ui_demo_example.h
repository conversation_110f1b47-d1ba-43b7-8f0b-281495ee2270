/**
 ******************************************************************************
 * @file    ui_demo_example.h
 * <AUTHOR>
 * @version V1.0.0
 * @date    2025-07-02
 * @brief   UI demo usage examples header file
 ******************************************************************************
 */

#ifndef __UI_DEMO_EXAMPLE_H
#define __UI_DEMO_EXAMPLE_H

#ifdef __cplusplus
extern "C" {
#endif

/* Includes ------------------------------------------------------------------*/
#include "ui_demo.h"
#include "FreeRTOS.h"
#include "task.h"

/* Exported types ------------------------------------------------------------*/
/* Exported constants --------------------------------------------------------*/
/* Exported macro ------------------------------------------------------------*/
/* Exported functions --------------------------------------------------------*/

/**
 * @brief Example 1: Basic UI test
 */
void UI_Example_BasicTest(void);

/**
 * @brief Example 2: INA226 data display UI
 */
void UI_Example_INA226Display(void);

/**
 * @brief Example 3: Combined UI with data updates
 */
void UI_Example_CombinedWithData(void);

/**
 * @brief Example 4: UI module lifecycle management
 */
void UI_Example_LifecycleManagement(void);

/**
 * @brief Run all UI demo examples
 */
void UI_RunAllExamples(void);

/**
 * @brief Simple UI task for FreeRTOS
 * @param pvParameters: Task parameters (unused)
 */
void UI_Task(void *pvParameters);

/**
 * @brief INA226 data display task for FreeRTOS
 * @param pvParameters: Task parameters (unused)
 */
void INA226_UI_Task(void *pvParameters);

#ifdef __cplusplus
}
#endif

#endif /* __UI_DEMO_EXAMPLE_H */
