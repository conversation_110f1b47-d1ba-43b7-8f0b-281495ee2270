/**
 ******************************************************************************
 * @file    ina226_example.c
 * <AUTHOR>
 * @version V1.0.0
 * @date    2025-07-02
 * @brief   INA226 usage examples
 ******************************************************************************
 */

/* Includes ------------------------------------------------------------------*/
#include "mod_ina226.h"
#include "app_util.h"

/* Private typedef -----------------------------------------------------------*/
/* Private define ------------------------------------------------------------*/
/* Private macro -------------------------------------------------------------*/
/* Private variables ---------------------------------------------------------*/
/* Private function prototypes -----------------------------------------------*/
/* Private functions ---------------------------------------------------------*/

/**
 * @brief Example 1: Basic voltage and current measurement
 */
void INA226_Example_BasicMeasurement(void)
{
    INA226_Data_t data;
    
    logi("=== INA226 Basic Measurement Example ===\r\n");
    
    // Initialize INA226
    if (!INA226_Init()) {
        logi("ERROR: INA226 initialization failed!\r\n");
        return;
    }
    
    logi("INA226 initialized successfully\r\n");
    
    // Take 10 measurements with 1 second interval
    for (int i = 0; i < 10; i++) {
        if (INA226_ReadData(&data)) {
            logi("Measurement %d:\r\n", i + 1);
            logi("  Bus Voltage: %.3f V\r\n", data.voltage_V);
            logi("  Shunt Voltage: %.3f mV\r\n", data.shunt_voltage_mV);
            logi("  Current: %.3f A\r\n", data.current_A);
            logi("  Power: %.3f W\r\n", data.power_W);
            logi("\r\n");
        } else {
            logi("ERROR: Failed to read measurement %d\r\n", i + 1);
        }
        
        s_delay_ms(1000); // Wait 1 second
    }
}

/**
 * @brief Example 2: Individual parameter reading
 */
void INA226_Example_IndividualReading(void)
{
    float voltage, current, power, shunt_voltage;
    
    logi("=== INA226 Individual Reading Example ===\r\n");
    
    // Read individual parameters
    voltage = INA226_ReadBusVoltage();
    current = INA226_ReadCurrent();
    power = INA226_ReadPower();
    shunt_voltage = INA226_ReadShuntVoltage();
    
    if (voltage >= 0 && current >= 0 && power >= 0 && shunt_voltage >= 0) {
        logi("Individual readings:\r\n");
        logi("  Bus Voltage: %.3f V\r\n", voltage);
        logi("  Current: %.3f A\r\n", current);
        logi("  Power: %.3f W\r\n", power);
        logi("  Shunt Voltage: %.3f mV\r\n", shunt_voltage);
    } else {
        logi("ERROR: One or more readings failed\r\n");
    }
}

/**
 * @brief Example 3: Custom configuration
 */
void INA226_Example_CustomConfig(void)
{
    INA226_Config_t config;
    INA226_Data_t data;
    
    logi("=== INA226 Custom Configuration Example ===\r\n");
    
    // Configure for high precision measurement
    config.avg_mode = INA226_CONFIG_AVG_128;           // 128 sample average
    config.bus_conv_time = INA226_CONFIG_VBUSCT_8244US; // 8.244ms conversion time
    config.shunt_conv_time = INA226_CONFIG_VSHCT_8244US; // 8.244ms conversion time
    config.mode = INA226_CONFIG_MODE_SHUNT_BUS_CONT;    // Continuous measurement
    
    if (INA226_Configure(&config)) {
        logi("Custom configuration applied successfully\r\n");
        
        // Wait for measurement to stabilize
        s_delay_ms(100);
        
        // Take a measurement with high precision settings
        if (INA226_ReadData(&data)) {
            logi("High precision measurement:\r\n");
            logi("  Bus Voltage: %.4f V\r\n", data.voltage_V);
            logi("  Shunt Voltage: %.4f mV\r\n", data.shunt_voltage_mV);
            logi("  Current: %.4f A\r\n", data.current_A);
            logi("  Power: %.4f W\r\n", data.power_W);
        } else {
            logi("ERROR: Failed to read high precision measurement\r\n");
        }
    } else {
        logi("ERROR: Failed to apply custom configuration\r\n");
    }
    
    // Restore default configuration
    config.avg_mode = INA226_CONFIG_AVG_1;
    config.bus_conv_time = INA226_CONFIG_VBUSCT_1100US;
    config.shunt_conv_time = INA226_CONFIG_VSHCT_1100US;
    config.mode = INA226_CONFIG_MODE_SHUNT_BUS_CONT;
    
    INA226_Configure(&config);
    logi("Default configuration restored\r\n");
}

/**
 * @brief Example 4: Power monitoring with thresholds
 */
void INA226_Example_PowerMonitoring(void)
{
    INA226_Data_t data;
    float voltage_threshold = 3.0f;  // 3V threshold
    float current_threshold = 0.1f;  // 100mA threshold
    float power_threshold = 0.5f;    // 0.5W threshold
    
    logi("=== INA226 Power Monitoring Example ===\r\n");
    logi("Monitoring thresholds:\r\n");
    logi("  Voltage: %.1f V\r\n", voltage_threshold);
    logi("  Current: %.3f A\r\n", current_threshold);
    logi("  Power: %.1f W\r\n", power_threshold);
    logi("\r\n");
    
    // Monitor for 30 seconds
    for (int i = 0; i < 30; i++) {
        if (INA226_ReadData(&data)) {
            bool alert = false;
            
            logi("Time %02ds - V:%.2fV I:%.3fA P:%.2fW", 
                 i + 1, data.voltage_V, data.current_A, data.power_W);
            
            // Check thresholds
            if (data.voltage_V < voltage_threshold) {
                logi(" [LOW VOLTAGE]");
                alert = true;
            }
            
            if (data.current_A > current_threshold) {
                logi(" [HIGH CURRENT]");
                alert = true;
            }
            
            if (data.power_W > power_threshold) {
                logi(" [HIGH POWER]");
                alert = true;
            }
            
            if (!alert) {
                logi(" [OK]");
            }
            
            logi("\r\n");
        } else {
            logi("Time %02ds - ERROR: Failed to read data\r\n", i + 1);
        }
        
        s_delay_ms(1000); // Wait 1 second
    }
}

/**
 * @brief Run all INA226 examples
 */
void INA226_RunAllExamples(void)
{
    logi("\r\n");
    logi("************************************************\r\n");
    logi("*          INA226 Examples Starting           *\r\n");
    logi("************************************************\r\n");
    
    // Example 1: Basic measurement
    INA226_Example_BasicMeasurement();
    s_delay_ms(2000);
    
    // Example 2: Individual reading
    INA226_Example_IndividualReading();
    s_delay_ms(2000);
    
    // Example 3: Custom configuration
    INA226_Example_CustomConfig();
    s_delay_ms(2000);
    
    // Example 4: Power monitoring
    INA226_Example_PowerMonitoring();
    
    logi("\r\n");
    logi("************************************************\r\n");
    logi("*          INA226 Examples Completed          *\r\n");
    logi("************************************************\r\n");
}
