# UI Demo Module

## 概述

UI Demo模块是从main.c中拆分出来的用户界面演示代码，基于LVGL图形库实现。该模块提供了基本的UI组件示例和INA226数据显示界面。

## 文件结构

```
User/
├── ui_demo.h              # UI演示模块头文件
├── ui_demo.c              # UI演示模块实现
├── ui_demo_example.h      # UI使用示例头文件
├── ui_demo_example.c      # UI使用示例实现
└── UI_DEMO_README.md      # 本说明文件
```

## 主要功能

### 1. 基本UI测试界面
- 显示"Hello LVGL!"标签
- 可点击按钮
- 动态更新计数器
- 定时器回调演示

### 2. INA226数据显示界面
- 电压显示
- 电流显示
- 功率显示
- 分流电压显示

### 3. 模块生命周期管理
- 初始化函数
- 清理函数
- 资源管理

## API接口

### 核心函数

```c
// 模块初始化
void ui_demo_init(void);

// 模块清理
void ui_demo_cleanup(void);

// 创建基本测试界面
void ui_test(void);

// 创建INA226数据显示界面
void ui_create_ina226_display(void);

// 更新INA226数据显示
void ui_update_ina226_data(float voltage, float current, float power, float shunt_voltage);
```

### 示例函数

```c
// 基本UI测试示例
void UI_Example_BasicTest(void);

// INA226显示示例
void UI_Example_INA226Display(void);

// 数据更新示例
void UI_Example_CombinedWithData(void);

// 生命周期管理示例
void UI_Example_LifecycleManagement(void);

// 运行所有示例
void UI_RunAllExamples(void);
```

### FreeRTOS任务

```c
// 简单UI任务
void UI_Task(void *pvParameters);

// INA226数据显示任务
void INA226_UI_Task(void *pvParameters);
```

## 使用方法

### 基本使用

```c
#include "ui_demo.h"

int main(void)
{
    // 初始化LVGL和显示驱动
    lv_init();
    lv_port_disp_init();
    
    // 初始化UI演示模块
    ui_demo_init();
    
    // 创建基本测试界面
    ui_test();
    
    // 主循环
    while (1) {
        lv_timer_handler();
        delay_ms(5);
    }
    
    return 0;
}
```

### INA226数据显示

```c
#include "ui_demo.h"
#include "mod_ina226.h"

void setup_ina226_display(void)
{
    INA226_Data_t data;
    
    // 初始化模块
    ui_demo_init();
    INA226_Init();
    
    // 创建显示界面
    ui_create_ina226_display();
    
    // 读取并更新数据
    if (INA226_ReadData(&data)) {
        ui_update_ina226_data(data.voltage_V, 
                              data.current_A, 
                              data.power_W, 
                              data.shunt_voltage_mV);
    }
}
```

### FreeRTOS集成

```c
#include "ui_demo_example.h"

void create_ui_tasks(void)
{
    // 创建UI任务
    xTaskCreate(UI_Task, "UI_Task", 256 * 4, NULL, 2, NULL);
    
    // 创建INA226数据显示任务
    xTaskCreate(INA226_UI_Task, "INA226_UI_Task", 256 * 4, NULL, 3, NULL);
}
```

## 代码结构

### ui_demo.c 主要组件

1. **全局变量**
   - `global_label`: 用于动态更新的标签对象

2. **私有函数**
   - `btn_event_handler()`: 按钮事件处理
   - `update_label_cb()`: 定时器回调函数

3. **公共函数**
   - `ui_test()`: 创建基本测试界面
   - `ui_create_ina226_display()`: 创建INA226显示界面
   - `ui_update_ina226_data()`: 更新数据显示

### 从main.c迁移的内容

以下内容已从main.c迁移到ui_demo.c：

- `btn_event_handler()` 函数
- `update_label_cb()` 函数
- `global_label` 全局变量
- `ui_test()` 函数

main.c中保留了对`ui_test()`的调用，确保功能不受影响。

## 自定义扩展

### 添加新的UI组件

```c
// 在ui_demo.c中添加新函数
void ui_create_custom_widget(void)
{
    lv_obj_t *scr = lv_scr_act();
    
    // 创建自定义组件
    lv_obj_t *slider = lv_slider_create(scr);
    lv_obj_set_size(slider, 200, 20);
    lv_obj_align(slider, LV_ALIGN_CENTER, 0, 60);
    
    // 添加事件处理
    lv_obj_add_event_cb(slider, slider_event_cb, LV_EVENT_VALUE_CHANGED, NULL);
}

// 在ui_demo.h中添加声明
void ui_create_custom_widget(void);
```

### 添加数据绑定

```c
// 创建数据更新函数
void ui_update_sensor_data(float temperature, float humidity)
{
    static lv_obj_t *temp_label = NULL;
    static lv_obj_t *hum_label = NULL;
    
    if (temp_label != NULL) {
        lv_label_set_text_fmt(temp_label, "Temp: %.1f°C", temperature);
    }
    
    if (hum_label != NULL) {
        lv_label_set_text_fmt(hum_label, "Humidity: %.1f%%", humidity);
    }
}
```

## 注意事项

1. **LVGL依赖**: 确保LVGL库已正确初始化
2. **内存管理**: 注意LVGL对象的生命周期管理
3. **线程安全**: 在多任务环境中注意LVGL的线程安全
4. **显示更新**: 定期调用`lv_timer_handler()`更新显示
5. **资源清理**: 使用`ui_demo_cleanup()`清理资源

## 故障排除

### 界面不显示
1. 检查LVGL初始化是否正确
2. 检查显示驱动是否正常
3. 确认`lv_timer_handler()`被定期调用

### 按钮无响应
1. 检查事件回调函数是否正确注册
2. 确认触摸输入设备已初始化
3. 检查按钮对象是否正确创建

### 数据不更新
1. 检查数据源是否正常
2. 确认更新函数被正确调用
3. 验证标签对象指针是否有效

## 更新日志

- **v1.0.0** (2025-07-02): 初始版本
  - 从main.c拆分UI相关代码
  - 实现基本UI测试界面
  - 添加INA226数据显示功能
  - 提供使用示例和FreeRTOS任务模板
